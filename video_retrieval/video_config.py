import os
import torch
from pathlib import Path
from dotenv import load_dotenv

# Call load_dotenv() early to load .env from standard locations (e.g., project root or CWD)
# This aligns with typical usage seen in reference config.py
dotenv_initial_load_status = load_dotenv()

# --- General Paths ---
# BASE_DIR is used for DATA paths, not for .env location anymore.
BASE_DIR = "/Users/<USER>"
VIDEO_ROOT_DIR = os.path.join(BASE_DIR, "video_database")
LONG_VIDEOS_DIR = os.path.join(VIDEO_ROOT_DIR, "long_videos")
CLIPS_DIR = os.path.join(VIDEO_ROOT_DIR, "clips")
FRAMES_DIR = os.path.join(VIDEO_ROOT_DIR, "frames_cache")
AUDIO_CACHE_DIR = os.path.join(VIDEO_ROOT_DIR, "audio_cache")
INDEXES_DIR = os.path.join(VIDEO_ROOT_DIR, "indexes")
DB_DIR = os.path.join(VIDEO_ROOT_DIR, "database")
METADATA_DB_PATH = os.path.join(DB_DIR, "video_library.sqlite3")
VISUAL_FAISS_INDEX_PATH = os.path.join(INDEXES_DIR, "visual_faiss.index")
TRANSCRIPT_FAISS_INDEX_PATH = os.path.join(INDEXES_DIR, "transcript_faiss.index")

# --- Downloader Specific Paths & Config ---
KEYWORDS_FILE_PATH = os.path.join(VIDEO_ROOT_DIR, "config", "keywords.txt")
MANUAL_IMPORT_DIR = os.path.join(VIDEO_ROOT_DIR, "manual_imports")
DOWNLOADER_USER_AGENT = "AI Video Retrieval Bot/1.0 (+https://github.com/your_repo)"

# --- API Keys & Rate Limits (from .env or environment variables) ---
PEXELS_API_KEY = os.getenv("PEXELS_API_KEY")
PIXABAY_API_KEY = os.getenv("PIXABAY_API_KEY")

# Pexels
PEXELS_RATE_LIMIT_CAPACITY = int(os.getenv("PEXELS_RATE_LIMIT_CAPACITY", 180))
PEXELS_RATE_LIMIT_PERIOD_SECONDS = int(os.getenv("PEXELS_RATE_LIMIT_PERIOD_SECONDS", 3600))
PEXELS_MAX_PAGES_PER_KEYWORD = int(os.getenv("PEXELS_MAX_PAGES_PER_KEYWORD", 50))
PEXELS_MAX_ATTEMPTS_FACTOR_FOR_NEW = int(os.getenv("PEXELS_MAX_ATTEMPTS_FACTOR_FOR_NEW", 5))

# Pixabay
PIXABAY_RATE_LIMIT_CAPACITY = int(os.getenv("PIXABAY_RATE_LIMIT_CAPACITY", 90))
PIXABAY_RATE_LIMIT_PERIOD_SECONDS = int(os.getenv("PIXABAY_RATE_LIMIT_PERIOD_SECONDS", 60))
PIXABAY_MAX_PAGES_PER_KEYWORD = int(os.getenv("PIXABAY_MAX_PAGES_PER_KEYWORD", 25))
PIXABAY_RETRY_DELAY_ON_429_SECONDS = int(os.getenv("PIXABAY_RETRY_DELAY_ON_429_SECONDS", 10))
PIXABAY_MAX_ATTEMPTS_FACTOR_FOR_NEW = int(os.getenv("PIXABAY_MAX_ATTEMPTS_FACTOR_FOR_NEW", 5))

# General API request settings
API_REQUEST_TIMEOUT_SECONDS = 30
DOWNLOAD_TIMEOUT_SECONDS = 300

# --- Device Configuration ---
DEVICE = "cpu"

# --- Phase 0: Video Splitting ---
WHISPER_MODEL_NAME = "medium"
DEFAULT_PYSCENEDETECT_PARAMS = {
    "content_detector": {"threshold": 27.0, "min_scene_len": 15 },
    "adaptive_detector": {"adaptive_threshold": 3.0, "min_scene_len": 15},
    "threshold_detector": {"threshold": 12.0, "min_scene_len": 15, "fade_bias": 0.0}
}
SENTENCE_EMBEDDING_MODEL = "all-MiniLM-L6-v2"
TEXT_SEGMENTATION_METHOD = "sentence_embeddings"
SENTENCE_SIMILARITY_THRESHOLD = 0.5
MIN_CLIP_DURATION_SEC = 15.0
MAX_CLIP_DURATION_SEC = 180.0
BOUNDARY_MERGE_WINDOW_SEC = 1.5
SHORT_VIDEO_NO_SPLIT_THRESHOLD_SEC = 60.0

# --- Phase 1: Indexing ---
FRAME_EXTRACT_FPS = 1
FRAME_SCALE = "224:224"
AUDIO_EXTRACT_SAMPLE_RATE = 16000
CLIP_MODEL_NAME_FOR_VISUAL_EMBEDDING = "Searchium-ai/clip4clip-webvid150k"
VISUAL_EMBEDDING_DIM = 512
TEXT_EMBEDDING_MODEL_FOR_INDEXING = "BAAI/bge-base-en-v1.5"
TEXT_EMBEDDING_DIM = 768

# --- Phase 2: Retrieval ---
TOP_K_RESULTS = 10
FUSION_WEIGHT_VISUAL = 0.5
FUSION_WEIGHT_TEXT = 0.5

# --- Clip Assignment ---
MIN_DURATION_BUFFER_SEC = 0.5  # Minimum extra duration buffer beyond audio duration

# --- Helper to create directories ---
_DIRECTORIES_ENSURED = False
def ensure_directories():
    global _DIRECTORIES_ENSURED
    if _DIRECTORIES_ENSURED:
        return
    root_path = Path(VIDEO_ROOT_DIR)
    config_path = root_path / "config"

    dirs_to_create = [
        root_path, LONG_VIDEOS_DIR, CLIPS_DIR, FRAMES_DIR,
        AUDIO_CACHE_DIR, INDEXES_DIR, DB_DIR,
        config_path, Path(MANUAL_IMPORT_DIR)
    ]
    for d_path in dirs_to_create:
        os.makedirs(str(d_path), exist_ok=True)

    keywords_file = Path(KEYWORDS_FILE_PATH)
    if not keywords_file.exists():
        keywords_file.parent.mkdir(parents=True, exist_ok=True)
        with open(keywords_file, 'w', encoding='utf-8') as f:
            f.write("# Add keywords here, one per line\n")
            f.write("example keyword 1\n")
            f.write("nature\n")
        print(f"Created placeholder keywords file: {keywords_file}")
    _DIRECTORIES_ENSURED = True

# Call it once at import to ensure directories are ready
ensure_directories()

# --- Configuration Load Status and API Key Warnings (Printed Once) ---
_CONFIG_WARNINGS_PRINTED = False
def print_config_load_status_and_warnings():
    global _CONFIG_WARNINGS_PRINTED
    if _CONFIG_WARNINGS_PRINTED:
        return

    if dotenv_initial_load_status:
        # python-dotenv since v0.21.0 returns the path of the loaded .env file or True/False
        # For older versions, it might just be True/False.
        if isinstance(dotenv_initial_load_status, str) and os.path.exists(dotenv_initial_load_status):
            print(f"Loaded environment variables from .env file: {dotenv_initial_load_status}")
        elif dotenv_initial_load_status is True:
            print(f"Successfully loaded .env file (path determined by python-dotenv default search).")
        # If False, the next message handles it.
    else:
        # Suggest common locations if .env wasn't found by default search
        # project_root_for_env_suggestion = Path(os.getcwd())
        # Path(__file__).resolve().parent.parent # If config is in a subfolder
        print(f"Warning: .env file not found by python-dotenv default search. Please ensure it's in the project root (e.g., alongside your main script) or another discoverable location. Relying on pre-set environment variables or defaults.")

    if not PEXELS_API_KEY:
        print("Warning: PEXELS_API_KEY is not set. Pexels downloader will not work.")
    if not PIXABAY_API_KEY:
        print("Warning: PIXABAY_API_KEY is not set. Pixabay downloader will not work.")

    _CONFIG_WARNINGS_PRINTED = True

# Print warnings after API keys have been attempted to be loaded via os.getenv
print_config_load_status_and_warnings()